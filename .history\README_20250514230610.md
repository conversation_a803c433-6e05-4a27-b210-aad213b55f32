# 全球电力数据可视化平台

基于Flask和Pyecharts的电力数据可视化大屏应用，展示全球电力生产和消费数据。

## 功能特点

- 全球电力生产分布地图
- 能源类型分布饼图
- 电力生产趋势折线图
- 国家对比分析
- 能源结构对比图
- 电力生产排名

## 安装和运行

### 环境要求

- Python 3.7+
- Flask
- Pyecharts
- Pandas

### 安装依赖

```bash
pip install flask pyecharts pandas
```

### 运行应用

1. 克隆或下载项目到本地
2. 进入项目目录
3. 运行Flask应用

```bash
python app.py
```

4. 在浏览器中访问: http://localhost:5000

## 数据来源

- `static/data/MES_0125.csv`: 全球电力生产和消费数据

## 项目结构

```
power-dashboard/
├── app.py                  # Flask应用主文件
├── static/                 # 静态资源目录
│   ├── css/                # CSS样式文件
│   │   └── style.css       # 样式表
│   ├── js/                 # JavaScript文件
│   │   └── dashboard.js    # 前端交互逻辑
│   └── data/               # 数据文件
│       └── MES_0125.csv    # 电力数据文件
└── templates/              # HTML模板目录
    └── index.html          # 主页模板
```

## 自定义和扩展

### 添加新图表

1. 在`app.py`中添加新的API路由
2. 在`templates/index.html`中添加图表容器
3. 在`static/js/dashboard.js`中添加加载逻辑

### 修改样式

修改`static/css/style.css`文件以自定义界面风格。

## 许可证

MIT 