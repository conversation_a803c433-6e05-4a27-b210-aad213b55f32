# 全球能耗数据可视化平台

这是一个基于Flask的全球能耗数据可视化分析平台，提供树状结构的多页面展示、用户认证系统和完整的数据管理功能。

## 功能特性

### 🔐 用户认证系统
- 登录/登出功能
- 用户管理（添加/删除用户）
- 会话管理和权限控制

### 📊 树状结构的可视化页面
- **仪表板概览**：总体数据展示和关键指标
- **能源分析**：能源类型排序表和详细分析图表
- **国家对比**：多国能源结构对比分析
- **数据管理**：数据筛选、搜索和导出功能
- **系统管理**：用户管理和系统设置

### 📈 丰富的图表类型
- 世界地图热力图
- 能源类型分布饼图
- 国家排名柱状图
- 趋势线图
- 能源结构对比图

### 🔍 数据分析功能
- 按能源类型从高到低排序的表格
- 可再生能源vs传统能源对比
- 国家间能源结构对比
- 数据筛选和搜索

### 🌐 完全汉化
- 所有界面文本均为中文
- 符合中文用户使用习惯

## 安装和运行

### 环境要求

- Python 3.7+
- Flask
- Pyecharts
- Pandas

### 安装依赖

```bash
pip install flask pyecharts pandas
```

### 运行应用

1. 克隆或下载项目到本地
2. 进入项目目录
3. 运行Flask应用

```bash
python app.py
```

4. 在浏览器中访问: http://localhost:5000

## 数据来源

- `static/data/MES_0125.csv`: 全球电力生产和消费数据

## 项目结构

```
power-dashboard/
├── app.py                  # Flask应用主文件
├── static/                 # 静态资源目录
│   ├── css/                # CSS样式文件
│   │   └── style.css       # 样式表
│   ├── js/                 # JavaScript文件
│   │   └── dashboard.js    # 前端交互逻辑
│   └── data/               # 数据文件
│       └── MES_0125.csv    # 电力数据文件
└── templates/              # HTML模板目录
    └── index.html          # 主页模板
```

## 自定义和扩展

### 添加新图表

1. 在`app.py`中添加新的API路由
2. 在`templates/index.html`中添加图表容器
3. 在`static/js/dashboard.js`中添加加载逻辑

### 修改样式

修改`static/css/style.css`文件以自定义界面风格。

## 许可证

MIT