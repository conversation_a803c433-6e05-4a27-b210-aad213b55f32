# 全球能耗数据可视化平台

这是一个基于Flask的全球能耗数据可视化分析平台，提供树状结构的多页面展示、用户认证系统和完整的数据管理功能。

## 功能特性

### 🔐 用户认证系统
- 登录/登出功能
- 用户管理（添加/删除用户）
- 会话管理和权限控制

### 📊 树状结构的可视化页面
- **仪表板概览**：总体数据展示和关键指标
- **能源分析**：能源类型排序表和详细分析图表
- **国家对比**：多国能源结构对比分析
- **数据管理**：数据筛选、搜索和导出功能
- **系统管理**：用户管理和系统设置

### 📈 丰富的图表类型
- 世界地图热力图
- 能源类型分布饼图
- 国家排名柱状图
- 趋势线图
- 能源结构对比图

### 🔍 数据分析功能
- 按能源类型从高到低排序的表格
- 可再生能源vs传统能源对比
- 国家间能源结构对比
- 数据筛选和搜索

### 🌐 完全汉化
- 所有界面文本均为中文
- 符合中文用户使用习惯

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行应用：
```bash
python app.py
```

3. 在浏览器中访问：http://localhost:5000

## 默认登录信息

- 用户名：`admin`
- 密码：`admin123`

## 页面结构

```
登录页面 (/login)
├── 仪表板 (/dashboard) - 总体概览和关键指标
├── 能源分析 (/energy-analysis) - 能源类型排序表和分析图表
├── 国家对比 (/country-comparison) - 多国对比分析
├── 数据管理 (/data-management) - 数据筛选、搜索和导出
└── 系统管理 (/admin) - 用户管理和系统设置
```

## 主要改进

1. **网页名称**：从"电力"改为"能耗"
2. **树状结构**：将原来的单页面拆分为多个专门的功能页面
3. **登录系统**：添加了完整的用户认证和管理功能
4. **排序表格**：新增按能源类型从高到低排序的数据表
5. **完全汉化**：所有界面文本都是中文

## 数据来源

- `static/data/MES_0125.csv`: 全球电力生产和消费数据

## 项目结构

```
power-dashboard/
├── app.py                  # Flask应用主文件
├── static/                 # 静态资源目录
│   ├── css/                # CSS样式文件
│   │   └── style.css       # 样式表
│   ├── js/                 # JavaScript文件
│   │   └── dashboard.js    # 前端交互逻辑
│   └── data/               # 数据文件
│       └── MES_0125.csv    # 电力数据文件
└── templates/              # HTML模板目录
    └── index.html          # 主页模板
```

## 自定义和扩展

### 添加新图表

1. 在`app.py`中添加新的API路由
2. 在`templates/index.html`中添加图表容器
3. 在`static/js/dashboard.js`中添加加载逻辑

### 修改样式

修改`static/css/style.css`文件以自定义界面风格。

## 许可证

MIT