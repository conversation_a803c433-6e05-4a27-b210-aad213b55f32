from flask import Flask, render_template, jsonify, request, session, redirect, url_for, flash
import pandas as pd
import os
import datetime
import logging
import sqlite3
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# 数据文件路径
DATA_FILE = os.path.join('static', 'data', 'MES_0125.csv')
DATABASE = 'users.db'

# 能源类型中英文对照表
ENERGY_TYPE_MAPPING = {
    'Coal': '煤炭',
    'Oil': '石油',
    'Gas': '天然气',
    'Nuclear': '核能',
    'Hydro': '水力发电',
    'Wind': '风力发电',
    'Solar PV': '太阳能光伏',
    'Solar thermal': '太阳能热发电',
    'Geothermal': '地热能',
    'Biofuels': '生物燃料',
    'Biomass': '生物质能',
    'Other renewables': '其他可再生能源',
    'Electricity': '电力',
    'Heat': '热能',
    'Total': '总计'
}

# 获取中文能源类型名称
def get_chinese_energy_name(english_name):
    return ENERGY_TYPE_MAPPING.get(english_name, english_name)

# 初始化数据库
def init_db():
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # 创建默认管理员账户
    admin_password = generate_password_hash('admin123')
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, password_hash)
        VALUES (?, ?)
    ''', ('admin', admin_password))

    conn.commit()
    conn.close()

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 加载数据
def load_data():
    try:
        df = pd.read_csv(DATA_FILE)
        return df
    except Exception as e:
        logger.error(f"加载数据文件错误: {str(e)}")
        return None

# 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute('SELECT id, password_hash FROM users WHERE username = ?', (username,))
        user = cursor.fetchone()
        conn.close()

        if user and check_password_hash(user[1], password):
            session['user_id'] = user[0]
            session['username'] = username
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'error')

    return render_template('login.html')

# 登出
@app.route('/logout')
def logout():
    session.clear()
    flash('已成功登出！', 'info')
    return redirect(url_for('login'))

# 首页路由 - 重定向到仪表板
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

# 仪表板主页
@app.route('/dashboard')
@login_required
def dashboard():
    now = datetime.datetime.now()
    return render_template('dashboard.html', now=now)

# 能源分析页面
@app.route('/energy-analysis')
@login_required
def energy_analysis():
    return render_template('energy_analysis.html')

# 国家对比页面
@app.route('/country-comparison')
@login_required
def country_comparison():
    return render_template('country_comparison.html')

# 数据管理页面
@app.route('/data-management')
@login_required
def data_management():
    now = datetime.datetime.now()
    return render_template('data_management.html', now=now)

# 管理页面
@app.route('/admin')
@login_required
def admin():
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('SELECT id, username, created_at FROM users ORDER BY created_at DESC')
    users = cursor.fetchall()
    conn.close()
    return render_template('admin.html', users=users)

# 添加用户
@app.route('/admin/add-user', methods=['POST'])
@login_required
def add_user():
    username = request.form['username']
    password = request.form['password']

    if not username or not password:
        flash('用户名和密码不能为空！', 'error')
        return redirect(url_for('admin'))

    password_hash = generate_password_hash(password)

    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute('INSERT INTO users (username, password_hash) VALUES (?, ?)',
                      (username, password_hash))
        conn.commit()
        conn.close()
        flash(f'用户 {username} 添加成功！', 'success')
    except sqlite3.IntegrityError:
        flash('用户名已存在！', 'error')

    return redirect(url_for('admin'))

# 删除用户
@app.route('/admin/delete-user/<int:user_id>')
@login_required
def delete_user(user_id):
    if user_id == session.get('user_id'):
        flash('不能删除当前登录的用户！', 'error')
        return redirect(url_for('admin'))

    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
    conn.commit()
    conn.close()
    flash('用户删除成功！', 'success')
    return redirect(url_for('admin'))

# API路由 - 获取概览数据
@app.route('/api/data/overview')
def get_overview_data():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 获取基本统计数据
        total_electricity = df['Value'].sum()
        country_count = df['Country'].nunique()
        energy_types = df['Product'].nunique()

        # 按能源类型分组统计
        energy_distribution = df.groupby('Product')['Value'].sum().to_dict()

        # 可再生能源占比计算
        renewable_sources = ['Hydro', 'Wind', 'Solar PV', 'Geothermal', 'Biofuels']
        renewable_value = df[df['Product'].isin(renewable_sources)]['Value'].sum()
        renewable_percentage = (renewable_value / total_electricity) * 100 if total_electricity > 0 else 0

        # 组装数据
        result = {
            "total_electricity": float(total_electricity),
            "country_count": int(country_count),
            "energy_types": int(energy_types),
            "energy_distribution": energy_distribution,
            "renewable_percentage": float(renewable_percentage)
        }

        return jsonify(result)
    except Exception as e:
        logger.error(f"处理概览数据错误: {str(e)}")
        return jsonify({"error": f"处理数据失败: {str(e)}"}), 500

# API路由 - 获取国家列表
@app.route('/api/data/countries')
def get_countries():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        countries = df['Country'].unique().tolist()
        return jsonify({"countries": countries})
    except Exception as e:
        logger.error(f"获取国家列表错误: {str(e)}")
        return jsonify({"error": f"获取国家列表失败: {str(e)}"}), 500

# API路由 - 获取特定国家数据
@app.route('/api/data/country/<country_name>')
def get_country_data(country_name):
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 过滤特定国家数据
        country_df = df[df['Country'] == country_name]

        if country_df.empty:
            return jsonify({"error": f"未找到国家: {country_name}"}), 404

        # 按能源类型分组
        energy_data = country_df.groupby('Product')['Value'].sum().to_dict()

        # 计算总电力和可再生能源占比
        total = country_df['Value'].sum()
        renewable_sources = ['Hydro', 'Wind', 'Solar PV', 'Geothermal', 'Biofuels']
        renewable = country_df[country_df['Product'].isin(renewable_sources)]['Value'].sum()
        renewable_percentage = (renewable / total) * 100 if total > 0 else 0

        # 组装结果
        result = {
            "country": country_name,
            "total_electricity": float(total),
            "energy_data": energy_data,
            "renewable_percentage": float(renewable_percentage)
        }

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取国家数据错误: {str(e)}")
        return jsonify({"error": f"获取国家数据失败: {str(e)}"}), 500

# API路由 - 获取趋势线图数据
@app.route('/api/chart/trend-line')
def get_trend_line_data():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 模拟时间序列数据（因为原始CSV没有时间序列）
        # 这里使用不同国家作为时间点的模拟数据
        countries = df['Country'].unique().tolist()[:12]  # 取前12个国家模拟12个月

        # 准备数据
        series_data = []
        for product in ['Coal', 'Oil', 'Gas', 'Nuclear', 'Hydro', 'Wind']:
            data_points = []
            for country in countries:
                product_value = df[(df['Country'] == country) & (df['Product'] == product)]['Value'].sum()
                # 确保值是浮点数
                data_points.append(float(product_value))

            series_data.append({
                "name": product,
                "data": data_points
            })

        result = {
            "categories": countries,
            "series": series_data
        }

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取趋势线图数据错误: {str(e)}")
        return jsonify({"error": f"获取趋势线图数据失败: {str(e)}"}), 500

# API路由 - 获取国家排名数据
@app.route('/api/chart/country-ranking')
def get_country_ranking():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 按国家分组计算总电力生产
        country_totals = df.groupby('Country')['Value'].sum().reset_index()

        # 排序并取前10名
        top_countries = country_totals.sort_values('Value', ascending=False).head(10)

        # 转换为列表格式
        ranking_data = []
        for _, row in top_countries.iterrows():
            ranking_data.append({
                "country": row['Country'],
                "value": float(row['Value'])  # 确保值是浮点数
            })

        return jsonify({"ranking": ranking_data})
    except Exception as e:
        logger.error(f"获取国家排名数据错误: {str(e)}")
        return jsonify({"error": f"获取国家排名数据失败: {str(e)}"}), 500

# API路由 - 获取世界地图数据
@app.route('/api/chart/world-map')
def get_world_map_data():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 按国家分组计算总电力生产
        country_data = df.groupby('Country')['Value'].sum().reset_index()

        # 国家名称映射
        country_name_map = {
            "People's Republic of China": "China",
            # 可以添加更多映射关系
        }

        # 准备地图数据
        map_data = []
        for _, row in country_data.iterrows():
            country_name = row['Country']
            # 应用国家名称映射
            if country_name in country_name_map:
                country_name = country_name_map[country_name]

            map_data.append({
                "name": country_name,
                "value": float(row['Value'])  # 确保值是浮点数
            })

        return jsonify({"data": map_data})
    except Exception as e:
        logger.error(f"获取世界地图数据错误: {str(e)}")
        return jsonify({"error": f"获取世界地图数据失败: {str(e)}"}), 500

# API路由 - 获取能源类型对比数据
@app.route('/api/chart/energy-comparison')
def get_energy_comparison():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 获取查询参数
        country1 = request.args.get('country1', 'China')
        country2 = request.args.get('country2', 'United States')

        # 过滤数据
        country1_data = df[df['Country'] == country1].groupby('Product')['Value'].sum().to_dict()
        country2_data = df[df['Country'] == country2].groupby('Product')['Value'].sum().to_dict()

        # 合并所有能源类型
        all_products = set(list(country1_data.keys()) + list(country2_data.keys()))

        # 组装比较数据
        comparison_data = []
        for product in all_products:
            comparison_data.append({
                "product": product,
                "country1": float(country1_data.get(product, 0)),  # 确保值是浮点数
                "country2": float(country2_data.get(product, 0))   # 确保值是浮点数
            })

        result = {
            "country1": country1,
            "country2": country2,
            "data": comparison_data
        }

        return jsonify(result)
    except Exception as e:
        logger.error(f"获取能源类型对比数据错误: {str(e)}")
        return jsonify({"error": f"获取能源类型对比数据失败: {str(e)}"}), 500

# API路由 - 获取能源结构数据
@app.route('/api/chart/energy-structure')
def get_energy_structure():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 获取前5个国家
        top_countries = df.groupby('Country')['Value'].sum().sort_values(ascending=False).head(5).index.tolist()

        # 主要能源类型
        main_products = ['Coal', 'Oil', 'Gas', 'Nuclear', 'Hydro', 'Wind', 'Solar PV']

        # 准备数据结构
        structure_data = {
            "countries": top_countries,
            "products": main_products,
            "data": []
        }

        # 对每个国家和能源类型计算数据
        for country in top_countries:
            country_data = df[df['Country'] == country]
            for product in main_products:
                product_value = country_data[country_data['Product'] == product]['Value'].sum()
                structure_data["data"].append({
                    "country": country,
                    "product": product,
                    "value": float(product_value)  # 确保值是浮点数
                })

        return jsonify(structure_data)
    except Exception as e:
        logger.error(f"获取能源结构数据错误: {str(e)}")
        return jsonify({"error": f"获取能源结构数据失败: {str(e)}"}), 500

# API路由 - 获取能源类型排序表
@app.route('/api/data/energy-ranking')
def get_energy_ranking():
    df = load_data()
    if df is None:
        return jsonify({"error": "数据加载失败"}), 500

    try:
        # 按能源类型分组并排序
        energy_ranking = df.groupby('Product')['Value'].sum().reset_index()
        energy_ranking = energy_ranking.sort_values('Value', ascending=False)

        # 转换为列表格式
        ranking_data = []
        for index, row in energy_ranking.iterrows():
            ranking_data.append({
                "rank": index + 1,
                "energy_type": get_chinese_energy_name(row['Product']),
                "energy_type_en": row['Product'],  # 保留英文名称用于内部处理
                "total_value": float(row['Value']),
                "percentage": float((row['Value'] / energy_ranking['Value'].sum()) * 100)
            })

        return jsonify({"ranking": ranking_data})
    except Exception as e:
        logger.error(f"获取能源类型排序错误: {str(e)}")
        return jsonify({"error": f"获取能源类型排序失败: {str(e)}"}), 500

# 启动服务器
if __name__ == '__main__':
    init_db()  # 初始化数据库
    app.run(host='0.0.0.0', debug=True)