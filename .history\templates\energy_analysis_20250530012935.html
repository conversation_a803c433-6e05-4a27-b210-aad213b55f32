{% extends "base.html" %}

{% block title %}能源分析 - 全球能耗数据可视化平台{% endblock %}

{% block page_title %}能源分析{% endblock %}

{% block content %}
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
    <!-- 能源类型排序表 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能源类型排序表</h3>
        <div id="energy-ranking-table" style="max-height: 500px; overflow-y: auto;">
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">排名</th>
                        <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">能源类型</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">总量 (GWh)</th>
                        <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6;">占比 (%)</th>
                    </tr>
                </thead>
                <tbody id="ranking-tbody">
                    <tr>
                        <td colspan="4" style="text-align: center; padding: 20px; color: #7f8c8d;">加载中...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 能源类型详细分布 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能源类型分布图</h3>
        <div id="energy-distribution-chart" class="chart" style="height: 400px;"></div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
    <!-- 可再生能源vs传统能源 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">可再生能源 vs 传统能源</h3>
        <div id="renewable-vs-traditional" class="chart" style="height: 300px;"></div>
    </div>

    <!-- 能源结构变化趋势 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能源结构趋势</h3>
        <div id="energy-structure-trend" class="chart" style="height: 300px;"></div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 数字格式化函数
function numberFormat(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toFixed(0);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadEnergyRankingTable();
    initEnergyDistributionChart();
    initRenewableVsTraditional();
    initEnergyStructureTrend();
});

// 加载能源类型排序表
function loadEnergyRankingTable() {
    fetch('/api/data/energy-ranking')
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                console.error('加载能源排序数据错误:', result.error);
                return;
            }

            const tbody = document.getElementById('ranking-tbody');
            const rankingData = result.ranking || [];

            if (rankingData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 20px; color: #7f8c8d;">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = rankingData.map((item, index) => `
                <tr style="border-bottom: 1px solid #dee2e6; ${index % 2 === 0 ? 'background: #f8f9fa;' : ''}">
                    <td style="padding: 12px; font-weight: bold; color: #2c3e50;">${item.rank}</td>
                    <td style="padding: 12px; color: #2c3e50;">${item.energy_type}</td>
                    <td style="padding: 12px; text-align: right; color: #2c3e50;">${numberFormat(item.total_value)}</td>
                    <td style="padding: 12px; text-align: right; color: #2c3e50;">${item.percentage.toFixed(2)}%</td>
                </tr>
            `).join('');
        })
        .catch(error => {
            console.error('获取能源排序数据失败:', error);
        });
}

// 初始化能源分布图表
function initEnergyDistributionChart() {
    const chartDom = document.getElementById('energy-distribution-chart');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/data/energy-ranking')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载能源分布数据错误:', result.error);
                return;
            }

            const rankingData = result.ranking || [];
            const categories = rankingData.map(item => item.energy_type);
            const values = rankingData.map(item => item.total_value);

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: categories,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'GWh'
                },
                series: [
                    {
                        name: '能耗量',
                        type: 'bar',
                        data: values,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: '#3498db' },
                                { offset: 1, color: '#2980b9' }
                            ])
                        }
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取能源分布数据失败:', error);
        });
}

// 初始化可再生能源vs传统能源图表
function initRenewableVsTraditional() {
    const chartDom = document.getElementById('renewable-vs-traditional');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/data/overview')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载概览数据错误:', result.error);
                return;
            }

            const energyData = result.energy_distribution || {};
            const renewableSources = ['水力发电', '风力发电', '太阳能光伏', '地热能', '生物燃料'];

            let renewableTotal = 0;
            let traditionalTotal = 0;

            for (const [key, value] of Object.entries(energyData)) {
                if (renewableSources.includes(key)) {
                    renewableTotal += value;
                } else {
                    traditionalTotal += value;
                }
            }

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} GWh ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    bottom: '5%',
                    left: 'center',
                    itemWidth: 14,
                    itemHeight: 10,
                    itemGap: 15,
                    textStyle: {
                        fontSize: 12,
                        color: '#333'
                    }
                },
                series: [
                    {
                        name: '能源类型',
                        type: 'pie',
                        radius: '45%',
                        center: ['50%', '45%'],
                        data: [
                            { value: renewableTotal, name: '可再生能源' },
                            { value: traditionalTotal, name: '传统能源' }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取概览数据失败:', error);
        });
}

// 初始化能源结构趋势图表
function initEnergyStructureTrend() {
    const chartDom = document.getElementById('energy-structure-trend');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/chart/trend-line')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载趋势数据错误:', result.error);
                return;
            }

            const categories = result.categories || [];
            const seriesData = result.series || [];

            // 只显示主要能源类型（现在是中文名称）
            const mainSeries = seriesData.filter(item =>
                ['煤炭', '石油', '天然气', '核能', '水力发电', '风力发电'].includes(item.name)
            );

            const series = mainSeries.map(item => ({
                name: item.name,
                type: 'line',
                stack: 'Total',
                areaStyle: {},
                data: item.data
            }));

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    top: '5%',
                    left: 'center',
                    itemWidth: 12,
                    itemHeight: 8,
                    itemGap: 10,
                    textStyle: {
                        fontSize: 10,
                        color: '#333'
                    },
                    data: mainSeries.map(item => item.name)
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: categories
                    }
                ],
                yAxis: [
                    {
                        type: 'value'
                    }
                ],
                series: series
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取趋势数据失败:', error);
        });
}
</script>
{% endblock %}
