# 全球能耗数据可视化平台

这是一个基于Flask的全球能耗数据可视化分析平台，提供树状结构的多页面展示、用户认证系统和完整的数据管理功能。

## 功能特性

### 🔐 用户认证系统
- 登录/登出功能
- 用户管理（添加/删除用户）
- 会话管理和权限控制

### 📊 树状结构的可视化页面
- **仪表板概览**：总体数据展示和关键指标
- **能源分析**：能源类型排序表和详细分析图表
- **国家对比**：多国能源结构对比分析
- **数据管理**：数据筛选、搜索和导出功能
- **系统管理**：用户管理和系统设置

### 📈 丰富的图表类型
- 世界地图热力图
- 能源类型分布饼图
- 国家排名柱状图
- 趋势线图
- 能源结构对比图

### 🔍 数据分析功能
- 按能源类型从高到低排序的表格
- 可再生能源vs传统能源对比
- 国家间能源结构对比
- 数据筛选和搜索

### 🌐 完全汉化
- 所有界面文本均为中文
- 符合中文用户使用习惯

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行应用：
```bash
python app.py
```

3. 在浏览器中访问：http://localhost:5000

## 默认登录信息

- 用户名：`admin`
- 密码：`admin123`

## 页面结构

```
登录页面 (/login)
├── 仪表板 (/dashboard) - 总体概览和关键指标
├── 能源分析 (/energy-analysis) - 能源类型排序表和分析图表
├── 国家对比 (/country-comparison) - 多国对比分析
├── 数据管理 (/data-management) - 数据筛选、搜索和导出
└── 系统管理 (/admin) - 用户管理和系统设置
```

## 主要改进

1. **网页名称**：从"电力"改为"能耗"
2. **树状结构**：将原来的单页面拆分为多个专门的功能页面
3. **登录系统**：添加了完整的用户认证和管理功能
4. **排序表格**：新增按能源类型从高到低排序的数据表
5. **完全汉化**：所有界面文本都是中文

## 数据源

数据来源于MES_0125.csv文件，包含全球各国的能耗数据。

## 技术栈

- **后端**：Flask, SQLite
- **前端**：HTML, CSS, JavaScript
- **图表**：ECharts
- **数据处理**：Pandas
- **认证**：Werkzeug Security

## 文件结构

```
├── app.py                 # 主应用文件
├── requirements.txt       # 依赖包列表
├── users.db              # 用户数据库（自动创建）
├── templates/            # 模板文件
│   ├── base.html         # 基础模板
│   ├── login.html        # 登录页面
│   ├── dashboard.html    # 仪表板
│   ├── energy_analysis.html    # 能源分析
│   ├── country_comparison.html # 国家对比
│   ├── data_management.html    # 数据管理
│   └── admin.html        # 系统管理
└── static/               # 静态文件
    ├── css/
    ├── js/
    └── data/
```

## API接口

- `/api/data/overview` - 获取概览数据
- `/api/data/countries` - 获取国家列表
- `/api/data/country/<name>` - 获取特定国家数据
- `/api/data/energy-ranking` - 获取能源类型排序数据
- `/api/chart/world-map` - 获取世界地图数据
- `/api/chart/trend-line` - 获取趋势线数据
- `/api/chart/country-ranking` - 获取国家排名数据