<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球电力数据可视化平台</title>
    <!-- 引入ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/map/js/world.js"></script>
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #1a1a2e;
            --secondary-color: #16213e;
            --accent-color: #0f3460;
            --highlight-color: #0088ff;
            --renewable-color: #4caf50;
            --fossil-color: #ff9800;
            --text-color: #ffffff;
            --text-secondary: #b8b8b8;
            --card-bg: rgba(26, 26, 46, 0.8);
            --panel-bg: rgba(22, 33, 62, 0.8);
            --border-radius: 8px;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 头部样式 */
        .dashboard-header {
            background-color: var(--primary-color);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--box-shadow);
        }

        .logo-container h1 {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-color);
        }

        .control-panel {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .time-selector, .filter-panel {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        select, button {
            background-color: var(--accent-color);
            color: var(--text-color);
            border: none;
            padding: 8px 15px;
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        /* 主体内容区样式 */
        .dashboard-content {
            display: grid;
            grid-template-columns: 250px 1fr 250px;
            gap: 20px;
            padding: 20px;
            flex: 1;
        }

        /* 侧边栏样式 */
        .left-sidebar, .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .panel {
            background-color: var(--panel-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: var(--box-shadow);
        }

        .panel h3 {
            margin-bottom: 15px;
            color: var(--text-color);
            border-bottom: 1px solid var(--accent-color);
            padding-bottom: 8px;
        }

        /* 卡片指标样式 */
        .kpi-cards {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .kpi-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            flex: 1;
            text-align: center;
            box-shadow: var(--box-shadow);
        }

        .kpi-card h4 {
            margin-bottom: 10px;
            color: var(--text-secondary);
        }

        .kpi-value {
            font-size: 28px;
            font-weight: bold;
            color: var(--highlight-color);
        }

        /* 图表容器样式 */
        .chart-container {
            background-color: var(--panel-bg);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: var(--box-shadow);
        }

        .chart-container h3 {
            margin-bottom: 15px;
            color: var(--text-color);
        }

        .chart {
            width: 100%;
            height: 300px;
        }

        .main-chart .chart {
            height: 400px;
        }

        /* 图表布局 */
        .charts-row {
            display: flex;
            gap: 20px;
        }

        .half-width {
            flex: 1;
        }

        .full-width {
            width: 100%;
        }

        /* 底部样式 */
        .dashboard-footer {
            padding: 20px;
            background-color: var(--primary-color);
        }

        .dashboard-copyright {
            text-align: center;
            margin-top: 20px;
            color: var(--text-secondary);
            font-size: 14px;
        }

        /* 国家列表样式 */
        .country-list-container {
            max-height: 300px;
            overflow-y: auto;
        }

        .country-item {
            padding: 8px 10px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 5px;
            transition: background-color 0.2s;
        }

        .country-item:hover, .country-item.active {
            background-color: var(--accent-color);
        }

        /* 国家对比选择器 */
        .country-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .country-select {
            flex: 1;
        }

        /* 能源类型详情 */
        .energy-type-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .energy-type-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .energy-type-name {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .energy-type-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .renewable {
            background-color: var(--renewable-color);
        }

        .fossil {
            background-color: var(--fossil-color);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dashboard-content {
                grid-template-columns: 200px 1fr 200px;
            }
        }

        @media (max-width: 992px) {
            .dashboard-content {
                grid-template-columns: 1fr;
            }
            
            .left-sidebar, .right-sidebar {
                display: none;
            }
            
            .charts-row {
                flex-direction: column;
            }
        }

        @media (max-width: 768px) {
            .kpi-cards {
                flex-direction: column;
            }
            
            .dashboard-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .control-panel {
                width: 100%;
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <!-- 头部区域 -->
    <header class="dashboard-header">
        <div class="logo-container">
            <h1>全球电力数据可视化平台</h1>
        </div>
        <div class="control-panel">
            <div class="time-selector">
                <label for="time-select">时间选择：</label>
                <select id="time-select" class="time-select">
                    <option value="Jan-25">2025年1月</option>
                    <option value="Feb-25">2025年2月</option>
                    <option value="Mar-25">2025年3月</option>
                </select>
            </div>
            <div class="filter-panel">
                <button id="filter-btn" class="filter-btn">筛选条件</button>
            </div>
        </div>
    </header>

    <!-- 主体内容区 -->
    <main class="dashboard-content">
        <!-- 左侧边栏 -->
        <aside class="left-sidebar">
            <div class="panel country-list-panel">
                <h3>国家列表</h3>
                <div id="country-list" class="country-list-container">
                    <!-- 国家列表会通过JS填充 -->
                </div>
            </div>
            <div class="panel country-ranking-panel">
                <h3>电力生产排名</h3>
                <div id="country-ranking" class="ranking-container"></div>
            </div>
        </aside>

        <!-- 中心内容区 -->
        <section class="main-content">
            <!-- 顶部卡片区 -->
            <div class="kpi-cards">
                <div class="kpi-card">
                    <h4>总电力生产</h4>
                    <div id="total-electricity" class="kpi-value">24,339.72 GWh</div>
                </div>
                <div class="kpi-card">
                    <h4>可再生能源占比</h4>
                    <div id="renewable-rate" class="kpi-value">44.0%</div>
                </div>
                <div class="kpi-card">
                    <h4>最终消费量</h4>
                    <div id="final-consumption" class="kpi-value">23,035.19 GWh</div>
                </div>
            </div>

            <!-- 主要图表区 -->
            <div class="chart-container main-chart">
                <h3>全球电力生产分布</h3>
                <div id="world-map" class="chart"></div>
            </div>

            <!-- 下方图表区域 -->
            <div class="charts-row">
                <div class="chart-container half-width">
                    <h3>能源类型分布</h3>
                    <div id="energy-pie" class="chart"></div>
                </div>
                <div class="chart-container half-width">
                    <h3>电力生产趋势</h3>
                    <div id="trend-line" class="chart"></div>
                </div>
            </div>
        </section>

        <!-- 右侧边栏 -->
        <aside class="right-sidebar">
            <div class="panel energy-type-panel">
                <h3>能源类型详情</h3>
                <div id="energy-type-details" class="energy-type-container">
                    <!-- 能源类型详情会通过JS填充 -->
                </div>
            </div>
            <div class="panel country-comparison-panel">
                <h3>国家对比</h3>
                <div class="country-selector">
                    <select id="country-select-1" class="country-select">
                        <option value="Australia">澳大利亚</option>
                        <option value="Austria">奥地利</option>
                        <option value="Belgium">比利时</option>
                    </select>
                    <select id="country-select-2" class="country-select">
                        <option value="Australia">澳大利亚</option>
                        <option value="Austria">奥地利</option>
                        <option value="Belgium" selected>比利时</option>
                    </select>
                </div>
                <div id="country-comparison" class="comparison-chart chart"></div>
            </div>
        </aside>
    </main>

    <!-- 底部区域 -->
    <footer class="dashboard-footer">
        <div class="chart-container full-width">
            <h3>各国能源结构对比</h3>
            <div id="energy-structure-comparison" class="chart"></div>
        </div>
        <div class="dashboard-copyright">
            <p>© 2023 全球电力数据可视化平台 | 数据来源: MES_0125.csv</p>
        </div>
    </footer>

    <script>
        // 模拟数据
        const mockData = {
            countries: ['Australia', 'Austria', 'Belgium', 'Canada', 'China', 'Finland', 'France', 'Germany', 'India', 'Japan', 'Russia', 'USA'],
            energyProduction: {
                'Australia': 24339.72,
                'Austria': 6586.90,
                'Belgium': 6768.43,
                'Canada': 52890.40,
                'China': 698520.30,
                'Finland': 5892.70,
                'France': 45362.80,
                'Germany': 43982.60,
                'India': 142853.20,
                'Japan': 78956.30,
                'Russia': 98765.40,
                'USA': 352841.90
            },
            energyTypes: {
                'Hydro': { value: 934.83, type: 'renewable' },
                'Solar': { value: 6377.02, type: 'renewable' },
                'Wind': { value: 3182.66, type: 'renewable' },
                'Nuclear': { value: 0, type: 'renewable' },
                'Natural Gas': { value: 3361.27, type: 'fossil' },
                'Coal': { value: 9948.22, type: 'fossil' },
                'Oil': { value: 318.12, type: 'fossil' }
            },
            timeData: {
                'Jan-25': 24339.72,
                'Feb-25': 25500.30,
                'Mar-25': 26200.50,
                'Apr-25': 25800.20,
                'May-25': 27300.80,
                'Jun-25': 28500.40
            }
        };

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 填充国家列表
            const countryList = document.getElementById('country-list');
            mockData.countries.forEach(country => {
                const countryItem = document.createElement('div');
                countryItem.className = 'country-item';
                countryItem.textContent = country;
                countryItem.addEventListener('click', function() {
                    document.querySelectorAll('.country-item').forEach(item => {
                        item.classList.remove('active');
                    });
                    this.classList.add('active');
                });
                countryList.appendChild(countryItem);
            });

            // 填充能源类型详情
            const energyTypeDetails = document.getElementById('energy-type-details');
            Object.entries(mockData.energyTypes).forEach(([name, data]) => {
                const item = document.createElement('div');
                item.className = 'energy-type-item';
                
                const nameSpan = document.createElement('div');
                nameSpan.className = 'energy-type-name';
                
                const indicator = document.createElement('span');
                indicator.className = `energy-type-indicator ${data.type}`;
                
                nameSpan.appendChild(indicator);
                nameSpan.appendChild(document.createTextNode(name));
                
                const valueSpan = document.createElement('div');
                valueSpan.className = 'energy-type-value';
                valueSpan.textContent = `${data.value.toLocaleString()} GWh`;
                
                item.appendChild(nameSpan);
                item.appendChild(valueSpan);
                energyTypeDetails.appendChild(item);
            });

            // 初始化世界地图
            const worldMapChart = echarts.init(document.getElementById('world-map'));
            const worldMapOption = {
                title: {
                    text: '全球电力生产分布',
                    left: 'center',
                    textStyle: {
                        color: '#fff'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}: {c} GWh'
                },
                visualMap: {
                    min: 0,
                    max: 700000,
                    text: ['高', '低'],
                    inRange: {
                        color: ['#0f3460', '#0088ff']
                    },
                    textStyle: {
                        color: '#fff'
                    }
                },
                series: [{
                    name: '电力生产 (GWh)',
                    type: 'map',
                    map: 'world',
                    roam: true,
                    emphasis: {
                        label: {
                            show: true
                        }
                    },
                    data: Object.entries(mockData.energyProduction).map(([name, value]) => {
                        return { name, value };
                    })
                }]
            };
            worldMapChart.setOption(worldMapOption);

            // 初始化能源饼图
            const energyPieChart = echarts.init(document.getElementById('energy-pie'));
            const energyPieOption = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} GWh ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    textStyle: {
                        color: '#fff'
                    }
                },
                color: ['#4caf50', '#8bc34a', '#cddc39', '#7e57c2', '#ff9800', '#f44336', '#795548'],
                series: [{
                    name: '能源类型',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: Object.entries(mockData.energyTypes).map(([name, data]) => {
                        return { name, value: data.value };
                    })
                }]
            };
            energyPieChart.setOption(energyPieOption);

            // 初始化趋势线图
            const trendLineChart = echarts.init(document.getElementById('trend-line'));
            const trendLineOption = {
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: Object.keys(mockData.timeData),
                    axisLabel: {
                        color: '#b8b8b8'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: '#b8b8b8'
                    }
                },
                series: [{
                    name: '电力生产',
                    type: 'line',
                    smooth: true,
                    data: Object.values(mockData.timeData),
                    lineStyle: {
                        color: '#0088ff',
                        width: 3
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(0, 136, 255, 0.5)' },
                            { offset: 1, color: 'rgba(0, 136, 255, 0.1)' }
                        ])
                    },
                    symbol: 'circle',
                    symbolSize: 8
                }]
            };
            trendLineChart.setOption(trendLineOption);

            // 初始化国家对比图
            const countryComparisonChart = echarts.init(document.getElementById('country-comparison'));
            updateCountryComparison('Australia', 'Belgium');

            // 国家选择器事件
            document.getElementById('country-select-1').addEventListener('change', function() {
                updateCountryComparison(this.value, document.getElementById('country-select-2').value);
            });

            document.getElementById('country-select-2').addEventListener('change', function() {
                updateCountryComparison(document.getElementById('country-select-1').value, this.value);
            });

            // 初始化能源结构对比图
            const energyStructureChart = echarts.init(document.getElementById('energy-structure-comparison'));
            const energyStructureOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['水力', '太阳能', '风能', '核能', '天然气', '煤炭', '石油'],
                    textStyle: {
                        color: '#fff'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['Australia', 'Austria', 'Belgium', 'Canada', 'China'],
                    axisLabel: {
                        color: '#b8b8b8'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: '#b8b8b8'
                    }
                },
                series: [
                    {
                        name: '水力',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [934.83, 456.78, 234.56, 5678.90, 15678.90]
                    },
                    {
                        name: '太阳能',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [6377.02, 1234.56, 345.67, 890.12, 25678.90]
                    },
                    {
                        name: '风能',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [3182.66, 1567.89, 456.78, 3456.78, 18765.43]
                    },
                    {
                        name: '核能',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [0, 0, 3765.43, 5678.90, 45678.90]
                    },
                    {
                        name: '天然气',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [3361.27, 890.12, 345.67, 2345.67, 89012.34]
                    },
                    {
                        name: '煤炭',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [9948.22, 1234.56, 567.89, 5678.90, 456789.12]
                    },
                    {
                        name: '石油',
                        type: 'bar',
                        stack: 'total',
                        emphasis: { focus: 'series' },
                        data: [318.12, 234.56, 123.45, 890.12, 45678.90]
                    }
                ],
                color: ['#4caf50', '#8bc34a', '#cddc39', '#7e57c2', '#ff9800', '#f44336', '#795548']
            };
            energyStructureChart.setOption(energyStructureOption);

            // 窗口大小变化时调整图表大小
            window.addEventListener('resize', function() {
                worldMapChart.resize();
                energyPieChart.resize();
                trendLineChart.resize();
                countryComparisonChart.resize();
                energyStructureChart.resize();
            });

            // 国家对比图更新函数
            function updateCountryComparison(country1, country2) {
                const comparisonOption = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { type: 'shadow' }
                    },
                    legend: {
                        data: [country1, country2],
                        textStyle: { color: '#fff' }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['总电力', '水力', '太阳能', '风能', '天然气', '煤炭', '石油'],
                        axisLabel: { color: '#b8b8b8' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: { color: '#b8b8b8' }
                    },
                    series: [
                        {
                            name: country1,
                            type: 'bar',
                            data: [
                                mockData.energyProduction[country1] || 0,
                                Math.random() * 1000,
                                Math.random() * 2000,
                                Math.random() * 1500,
                                Math.random() * 2500,
                                Math.random() * 3500,
                                Math.random() * 500
                            ],
                            itemStyle: { color: '#0088ff' }
                        },
                        {
                            name: country2,
                            type: 'bar',
                            data: [
                                mockData.energyProduction[country2] || 0,
                                Math.random() * 1000,
                                Math.random() * 2000,
                                Math.random() * 1500,
                                Math.random() * 2500,
                                Math.random() * 3500,
                                Math.random() * 500
                            ],
                            itemStyle: { color: '#ff9800' }
                        }
                    ]
                };
                countryComparisonChart.setOption(comparisonOption);
            }
            
            // 初始化排名图表
            const countryRankingChart = echarts.init(document.getElementById('country-ranking'));
            const rankingData = Object.entries(mockData.energyProduction)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5);
            
            const rankingOption = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'shadow' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: { color: '#b8b8b8' }
                },
                yAxis: {
                    type: 'category',
                    data: rankingData.map(item => item[0]),
                    axisLabel: { color: '#b8b8b8' }
                },
                series: [{
                    name: '电力生产 (GWh)',
                    type: 'bar',
                    data: rankingData.map(item => item[1]),
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                            { offset: 0, color: '#0f3460' },
                            { offset: 1, color: '#0088ff' }
                        ])
                    }
                }]
            };
            countryRankingChart.setOption(rankingOption);
        });
    </script>
</body>
</html> 