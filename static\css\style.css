/* 全球电力数据可视化平台 - 样式文件 */

/* 基础样式设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    color: #333;
    background-color: #f5f7fa;
    line-height: 1.5;
}

/* 布局容器 */
.dashboard-header {
    background-color: #0f3460;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dashboard-content {
    display: flex;
    min-height: calc(100vh - 180px);
    padding: 20px;
    gap: 20px;
}

.dashboard-footer {
    padding: 15px 20px;
    background-color: #f0f2f5;
    border-top: 1px solid #e0e0e0;
}

/* 头部样式 */
.logo-container h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.control-panel {
    display: flex;
    gap: 15px;
    align-items: center;
}

.time-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.time-select {
    background-color: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #000;
    padding: 5px 10px;
    border-radius: 4px;
}

/* 为下拉框选项设置样式 */
.time-select option {
    background-color: #fff;
    color: #333;
}

.filter-btn {
    background-color: #0088ff;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.filter-btn:hover {
    background-color: #0066cc;
}

/* 侧边栏样式 */
.left-sidebar, .right-sidebar {
    width: 250px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 面板样式 */
.panel {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.panel h3 {
    padding: 12px 15px;
    margin: 0;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

/* 国家列表面板 */
.country-list-panel {
    flex: 1;
}

.country-list-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 5px 0;
}

.country-item {
    padding: 8px 15px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.country-item:hover {
    background-color: #f0f7ff;
}

.country-item.selected {
    background-color: #e6f2ff;
    font-weight: 600;
    border-left: 3px solid #0088ff;
}

/* 国家排名面板 */
.country-ranking-panel {
    height: 300px;
}

.ranking-container {
    height: calc(100% - 40px);
}

/* 能源类型详情面板 */
.energy-type-panel {
    flex: 1;
}

.energy-type-container {
    padding: 15px;
}

.country-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.country-header h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.energy-type-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.energy-type-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px dashed #e0e0e0;
}

.energy-type-name {
    font-weight: 500;
}

.energy-type-value {
    font-weight: 600;
    color: #0088ff;
}

/* 国家对比面板 */
.country-comparison-panel {
    height: 350px;
}

.country-selector {
    padding: 15px;
    display: flex;
    gap: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.country-select {
    flex: 1;
    padding: 5px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.comparison-chart {
    height: calc(100% - 90px);
}

/* KPI卡片样式 */
.kpi-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 5px;
}

.kpi-card {
    flex: 1;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    text-align: center;
}

.kpi-card h4 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 10px;
}

.kpi-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #0088ff;
}

/* 图表容器样式 */
.chart-container {
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.chart-container h3 {
    padding: 12px 15px;
    margin: 0;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 1rem;
    font-weight: 600;
}

.chart {
    width: 100%;
    height: calc(100% - 40px);
    min-height: 300px;
}

.main-chart {
    flex: 1;
    min-height: 400px;
}

.half-width {
    flex: 1;
    min-height: 300px;
}

.charts-row {
    display: flex;
    gap: 20px;
    flex: 1;
}

.full-width {
    width: 100%;
    height: 300px;
}

/* 底部版权信息 */
.dashboard-copyright {
    text-align: center;
    margin-top: 20px;
    font-size: 0.8rem;
    color: #888;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .dashboard-content {
        flex-direction: column;
    }
    
    .left-sidebar, .right-sidebar {
        width: 100%;
        flex-direction: row;
    }
    
    .panel {
        flex: 1;
    }
    
    .charts-row {
        flex-direction: column;
    }
    
    .country-list-panel {
        height: 300px;
    }
    
    .energy-type-panel {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .control-panel {
        width: 100%;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .kpi-cards {
        flex-direction: column;
    }
    
    .left-sidebar, .right-sidebar {
        flex-direction: column;
    }
}