// 全球电力数据可视化平台 - 前端交互脚本
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有图表
    initWorldMap();
    initEnergyPie();
    initTrendLine();
    initCountryRanking();
    initEnergyStructure();
    initCountryList();
    loadOverviewData();
    
    // 事件监听
    setupEventListeners();
    
    // 监听全屏变更事件
    document.addEventListener('fullscreenchange', function() {
        // 重新调整所有图表大小
        resizeAllCharts();
    });
    document.addEventListener('webkitfullscreenchange', function() {
        resizeAllCharts();
    });
    document.addEventListener('mozfullscreenchange', function() {
        resizeAllCharts();
    });
    document.addEventListener('MSFullscreenChange', function() {
        resizeAllCharts();
    });
});

// 重新调整所有图表大小
function resizeAllCharts() {
    const chartContainers = document.querySelectorAll('.chart');
    chartContainers.forEach(container => {
        const chart = echarts.getInstanceByDom(container);
        if (chart) {
            chart.resize();
        }
    });
}

// 初始化事件监听器
function setupEventListeners() {
    // 时间选择器事件
    const timeSelect = document.getElementById('time-select');
    if (timeSelect) {
        timeSelect.addEventListener('change', function() {
            // 更新所有图表
            updateAllCharts();
        });
    }
    
    // 国家选择器事件
    const countrySelect1 = document.getElementById('country-select-1');
    const countrySelect2 = document.getElementById('country-select-2');
    
    if (countrySelect1 && countrySelect2) {
        countrySelect1.addEventListener('change', updateCountryComparison);
        countrySelect2.addEventListener('change', updateCountryComparison);
    }
}

// 加载概览数据
function loadOverviewData() {
    fetch('/api/data/overview')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('加载概览数据错误:', data.error);
                return;
            }
            
            // 更新KPI卡片
            document.getElementById('total-electricity').textContent = 
                numberFormat(data.total_electricity) + ' GWh';
            
            document.getElementById('renewable-rate').textContent = 
                data.renewable_percentage.toFixed(2) + '%';
            
            document.getElementById('final-consumption').textContent = 
                numberFormat(data.total_electricity * 0.8) + ' GWh'; // 假设最终消费量是总电力的80%
        })
        .catch(error => {
            console.error('获取概览数据失败:', error);
        });
}

// 初始化世界地图
function initWorldMap() {
    // 创建地图容器
    const chartDom = document.getElementById('world-map');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    // 获取地图数据
    fetch('/api/chart/world-map')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载世界地图数据错误:', result.error);
                return;
            }
            
            const data = result.data || [];
            const maxValue = Math.max(...data.map(item => item.value));
            
            // 国家名称映射，确保API返回的国家名称与地图中的名称匹配
            const nameMap = {
                'United States': 'United States',
                'China': 'China',
                'Russia': 'Russia',
                'Canada': 'Canada',
                'Australia': 'Australia',
                'Brazil': 'Brazil',
                'India': 'India',
                'United Kingdom': 'United Kingdom',
                'France': 'France',
                'Germany': 'Germany',
                'Japan': 'Japan',
                'South Africa': 'South Africa',
                'Mexico': 'Mexico',
                'Indonesia': 'Indonesia'
                // 如果有更多国家，可以在这里添加
            };
            
            const option = {
                title: {
                    text: '全球电力生产分布',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}<br/>{c} GWh'
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' },
                        dataZoom: { title: '区域缩放' }
                    }
                },
                visualMap: {
                    min: 0,
                    max: maxValue,
                    text: ['高', '低'],
                    calculable: true,
                    inRange: {
                        color: ['#0f3460', '#0088ff']
                    },
                    textStyle: {
                        color: '#b8b8b8'
                    }
                },
                series: [
                    {
                        name: '电力生产',
                        type: 'map',
                        map: 'world',
                        roam: true,
                        nameMap: nameMap,
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        data: data
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取世界地图数据失败:', error);
        });
}

// 初始化能源类型饼图
function initEnergyPie() {
    const chartDom = document.getElementById('energy-pie');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    // 获取概览数据中的能源分布
    fetch('/api/data/overview')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载能源分布数据错误:', result.error);
                return;
            }
            
            const energyData = result.energy_distribution || {};
            
            // 过滤和处理数据，只显示主要能源类型
            const mainEnergyTypes = ['Coal', 'Oil', 'Gas', 'Nuclear', 'Hydro', 'Wind', 'Solar PV'];
            const pieData = [];
            
            let otherValue = 0;
            
            for (const [key, value] of Object.entries(energyData)) {
                if (mainEnergyTypes.includes(key)) {
                    pieData.push({ name: key, value: value });
                } else {
                    otherValue += value;
                }
            }
            
            if (otherValue > 0) {
                pieData.push({ name: '其他', value: otherValue });
            }
            
            const option = {
                title: {
                    text: '能源类型分布',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} GWh ({d}%)'
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' }
                    }
                },
                legend: {
                    orient: 'vertical',
                    right: '10%',
                    top: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                series: [
                    {
                        name: '能源类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '14',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: pieData
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取能源分布数据失败:', error);
        });
}

// 初始化趋势线图
function initTrendLine() {
    const chartDom = document.getElementById('trend-line');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    fetch('/api/chart/trend-line')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载趋势线图数据错误:', result.error);
                return;
            }
            
            const categories = result.categories || [];
            const seriesData = result.series || [];
            
            const series = seriesData.map(item => {
                return {
                    name: item.name,
                    type: 'line',
                    smooth: true,
                    data: item.data
                };
            });
            
            const option = {
                title: {
                    text: '电力生产趋势',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' },
                        dataZoom: { title: '区域缩放' }
                    }
                },
                legend: {
                    data: seriesData.map(item => item.name),
                    textStyle: {
                        color: '#333'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: categories,
                    axisLabel: {
                        color: '#333'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: '#333'
                    }
                },
                series: series
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取趋势线图数据失败:', error);
        });
}

// 初始化国家排名
function initCountryRanking() {
    const chartDom = document.getElementById('country-ranking');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    fetch('/api/chart/country-ranking')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载国家排名数据错误:', result.error);
                return;
            }
            
            const rankingData = result.ranking || [];
            
            // 翻转数组以便正确显示
            const countries = rankingData.map(item => item.country).reverse();
            const values = rankingData.map(item => item.value).reverse();
            
            const option = {
                title: {
                    text: '电力生产排名',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' }
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value',
                    axisLabel: {
                        color: '#333'
                    }
                },
                yAxis: {
                    type: 'category',
                    data: countries,
                    axisLabel: {
                        color: '#333'
                    }
                },
                series: [
                    {
                        name: '电力生产',
                        type: 'bar',
                        data: values,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#0f3460' },
                                { offset: 1, color: '#0088ff' }
                            ])
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取国家排名数据失败:', error);
        });
}

// 初始化能源结构对比
function initEnergyStructure() {
    const chartDom = document.getElementById('energy-structure-comparison');
    if (!chartDom) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    fetch('/api/chart/energy-structure')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载能源结构数据错误:', result.error);
                return;
            }
            
            const countries = result.countries || [];
            const products = result.products || [];
            const data = result.data || [];
            
            // 转换数据格式以符合ECharts要求
            const formattedData = [];
            const seriesData = [];
            
            // 按国家分组计算总电力
            const countryTotals = {};
            countries.forEach(country => {
                countryTotals[country] = 0;
            });
            
            data.forEach(item => {
                countryTotals[item.country] += item.value;
            });
            
            // 计算每种能源在各国的占比
            data.forEach(item => {
                const value = (item.value / countryTotals[item.country]) * 100;
                formattedData.push([
                    item.country,
                    item.product,
                    value.toFixed(2)
                ]);
            });
            
            // 为每种能源类型创建系列
            products.forEach(product => {
                seriesData.push({
                    name: product,
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: true,
                        formatter: function(params) {
                            return params.value[2] > 5 ? params.value[2] + '%' : '';
                        }
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: countries.map(country => {
                        const item = data.find(d => d.country === country && d.product === product);
                        const value = item ? (item.value / countryTotals[country]) * 100 : 0;
                        return value.toFixed(2);
                    })
                });
            });
            
            const option = {
                title: {
                    text: '各国能源结构对比',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let res = params[0].name + '<br/>';
                        let sum = 0;
                        
                        params.forEach(item => {
                            res += item.marker + ' ' + item.seriesName + ': ' + item.value + '%<br/>';
                            sum += parseFloat(item.value);
                        });
                        
                        res += '<br/>总计: ' + sum.toFixed(2) + '%';
                        return res;
                    }
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' }
                    }
                },
                legend: {
                    data: products
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: countries,
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '百分比',
                    max: 100
                },
                series: seriesData
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
            
            // 窗口大小变化时，重新调整图表大小
            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取能源结构数据失败:', error);
        });
}

// 初始化国家列表
function initCountryList() {
    const countryListContainer = document.getElementById('country-list');
    if (!countryListContainer) return;
    
    fetch('/api/data/countries')
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                console.error('加载国家列表错误:', result.error);
                return;
            }
            
            const countries = result.countries || [];
            
            // 清空容器
            countryListContainer.innerHTML = '';
            
            // 创建国家列表元素
            countries.forEach(country => {
                const countryItem = document.createElement('div');
                countryItem.className = 'country-item';
                countryItem.textContent = country;
                
                // 点击事件 - 显示国家详情
                countryItem.addEventListener('click', function() {
                    showCountryDetails(country);
                });
                
                countryListContainer.appendChild(countryItem);
            });
            
            // 更新国家选择下拉框
            updateCountrySelects(countries);
        })
        .catch(error => {
            console.error('获取国家列表失败:', error);
        });
}

// 更新国家选择下拉框
function updateCountrySelects(countries) {
    const select1 = document.getElementById('country-select-1');
    const select2 = document.getElementById('country-select-2');
    
    if (!select1 || !select2 || !countries) return;
    
    // 清空下拉框
    select1.innerHTML = '';
    select2.innerHTML = '';
    
    // 添加国家选项
    countries.forEach((country, index) => {
        const option1 = document.createElement('option');
        option1.value = country;
        option1.textContent = country;
        
        const option2 = document.createElement('option');
        option2.value = country;
        option2.textContent = country;
        
        select1.appendChild(option1);
        select2.appendChild(option2);
        
        // 默认选择不同的国家
        if (index === 0) {
            option1.selected = true;
        } else if (index === 1) {
            option2.selected = true;
        }
    });
    
    // 初始化国家对比图表
    updateCountryComparison();
}

// 显示国家详情
function showCountryDetails(country) {
    fetch(`/api/data/country/${encodeURIComponent(country)}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('加载国家详情错误:', data.error);
                return;
            }
            
            // 高亮选中的国家
            const countryItems = document.querySelectorAll('.country-item');
            countryItems.forEach(item => {
                if (item.textContent === country) {
                    item.classList.add('selected');
                } else {
                    item.classList.remove('selected');
                }
            });
            
            // 更新能源类型详情面板
            const detailsContainer = document.getElementById('energy-type-details');
            if (detailsContainer) {
                let detailsHtml = `
                    <div class="country-header">
                        <h4>${country}</h4>
                        <p>总电力: ${numberFormat(data.total_electricity)} GWh</p>
                        <p>可再生能源: ${data.renewable_percentage.toFixed(2)}%</p>
                    </div>
                    <div class="energy-type-list">
                `;
                
                // 能源类型详情
                for (const [type, value] of Object.entries(data.energy_data)) {
                    if (value > 0) {  // 只显示有数据的能源类型
                        detailsHtml += `
                            <div class="energy-type-item">
                                <span class="energy-type-name">${type}</span>
                                <span class="energy-type-value">${numberFormat(value)} GWh</span>
                            </div>
                        `;
                    }
                }
                
                detailsHtml += '</div>';
                detailsContainer.innerHTML = detailsHtml;
            }
        })
        .catch(error => {
            console.error('获取国家详情失败:', error);
        });
}

// 更新国家对比图表
function updateCountryComparison() {
    const chartDom = document.getElementById('country-comparison');
    if (!chartDom) return;
    
    // 获取选择的国家
    const country1 = document.getElementById('country-select-1').value;
    const country2 = document.getElementById('country-select-2').value;
    
    if (!country1 || !country2) return;
    
    const chart = echarts.init(chartDom);
    chart.showLoading();
    
    fetch(`/api/chart/energy-comparison?country1=${country1}&country2=${country2}`)
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();
            
            if (result.error) {
                console.error('加载国家对比数据错误:', result.error);
                return;
            }
            
            const comparisonData = result.data || [];
            
            // 准备数据
            const categories = comparisonData.map(item => item.product);
            const country1Data = comparisonData.map(item => item.country1);
            const country2Data = comparisonData.map(item => item.country2);
            
            const option = {
                title: {
                    text: '国家能源对比',
                    left: 'center',
                    top: 10,
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                toolbox: {
                    feature: {
                        saveAsImage: { title: '保存为图片' },
                        dataView: { title: '数据视图', readOnly: true },
                        restore: { title: '重置' },
                        magicType: { 
                            type: ['line', 'bar', 'stack'],
                            title: {
                                line: '切换为折线图',
                                bar: '切换为柱状图',
                                stack: '切换为堆叠'
                            }
                        }
                    }
                },
                legend: {
                    data: [result.country1, result.country2],
                    textStyle: {
                        color: '#333'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: categories,
                    axisLabel: {
                        interval: 0,
                        rotate: 30,
                        color: '#333'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: '#333'
                    }
                },
                series: [
                    {
                        name: result.country1,
                        type: 'bar',
                        data: country1Data,
                        itemStyle: {
                            color: '#0088ff'
                        }
                    },
                    {
                        name: result.country2,
                        type: 'bar',
                        data: country2Data,
                        itemStyle: {
                            color: '#ff7e00'
                        }
                    }
                ]
            };
            
            chart.setOption(option);
            
            // 添加双击全屏事件
            enableChartFullscreen(chartDom, chart);
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取国家对比数据失败:', error);
        });
}

// 更新所有图表
function updateAllCharts() {
    initWorldMap();
    initEnergyPie();
    initTrendLine();
    initCountryRanking();
    initEnergyStructure();
    loadOverviewData();
    
    // 如果有选中的国家，更新国家详情
    const selectedCountry = document.querySelector('.country-item.selected');
    if (selectedCountry) {
        showCountryDetails(selectedCountry.textContent);
    }
    
    // 更新国家对比
    updateCountryComparison();
}

// 格式化数字，添加千位分隔符
function numberFormat(num) {
    return new Intl.NumberFormat().format(Math.round(num));
}

// 为图表启用双击全屏功能
function enableChartFullscreen(chartDom, chart) {
    if (!chartDom || !chart) return;
    
    // 添加双击全屏事件
    chartDom.addEventListener('dblclick', function() {
        const fullscreenElement = document.fullscreenElement || 
                                 document.webkitFullscreenElement || 
                                 document.mozFullScreenElement || 
                                 document.msFullscreenElement;
        if (!fullscreenElement) {
            if (chartDom.requestFullscreen) {
                chartDom.requestFullscreen();
            } else if (chartDom.webkitRequestFullscreen) {
                chartDom.webkitRequestFullscreen();
            } else if (chartDom.mozRequestFullScreen) {
                chartDom.mozRequestFullScreen();
            } else if (chartDom.msRequestFullscreen) {
                chartDom.msRequestFullscreen();
            }
            
            // 全屏后重新调整大小
            setTimeout(function() {
                chart.resize();
            }, 100);
        }
    });
}