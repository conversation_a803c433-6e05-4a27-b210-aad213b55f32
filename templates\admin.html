{% extends "base.html" %}

{% block title %}系统管理 - 全球能耗数据可视化平台{% endblock %}

{% block page_title %}系统管理{% endblock %}

{% block content %}
<!-- 系统概览 -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">注册用户数</h4>
        <div style="font-size: 24px; font-weight: bold; color: #2c3e50;">{{ users|length }}</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">当前用户</h4>
        <div style="font-size: 16px; color: #3498db;">{{ session.username }}</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">系统状态</h4>
        <div style="font-size: 16px; color: #27ae60;">正常运行</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">最后更新</h4>
        <div style="font-size: 14px; color: #2c3e50;">刚刚</div>
    </div>
</div>

<!-- 用户管理 -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
    <h3 style="margin-bottom: 20px; color: #2c3e50;">用户管理</h3>

    <!-- 添加用户表单 -->
    <div style="background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
        <h4 style="margin-bottom: 15px; color: #2c3e50;">添加新用户</h4>
        <form method="POST" action="{{ url_for('add_user') }}" style="display: grid; grid-template-columns: 1fr 1fr auto; gap: 15px; align-items: end;">
            <div>
                <label for="username" style="display: block; margin-bottom: 5px; color: #7f8c8d;">用户名:</label>
                <input type="text" id="username" name="username" required
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div>
                <label for="password" style="display: block; margin-bottom: 5px; color: #7f8c8d;">密码:</label>
                <input type="password" id="password" name="password" required
                       style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            <div>
                <button type="submit"
                        style="background: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                    添加用户
                </button>
            </div>
        </form>
    </div>

    <!-- 用户列表 -->
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa;">
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">用户名</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6;">创建时间</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">操作</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr style="border-bottom: 1px solid #dee2e6; {% if loop.index % 2 == 0 %}background: #f8f9fa;{% endif %}">
                    <td style="padding: 12px; color: #2c3e50;">{{ user[0] }}</td>
                    <td style="padding: 12px; color: #2c3e50;">
                        {{ user[1] }}
                        {% if user[0] == session.user_id %}
                            <span style="background: #3498db; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">当前用户</span>
                        {% endif %}
                    </td>
                    <td style="padding: 12px; color: #7f8c8d;">{{ user[2] }}</td>
                    <td style="padding: 12px; text-align: center;">
                        {% if user[0] != session.user_id %}
                            <button onclick="deleteUser({{ user[0] }}, '{{ user[1] }}')"
                                    style="background: #e74c3c; color: white; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                                删除
                            </button>
                        {% else %}
                            <span style="color: #7f8c8d; font-size: 12px;">-</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- 系统设置 -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h3 style="margin-bottom: 20px; color: #2c3e50;">系统设置</h3>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
        <!-- 数据源设置 -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <h4 style="margin-bottom: 10px; color: #2c3e50;">数据源设置</h4>
            <div style="margin-bottom: 10px;">
                <label style="display: block; margin-bottom: 5px; color: #7f8c8d;">当前数据文件:</label>
                <input type="text" value="MES_0125.csv" readonly
                       style="width: 100%; padding: 6px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
            </div>
            <button onclick="refreshDataSource()"
                    style="background: #f39c12; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                刷新数据源
            </button>
        </div>

        <!-- 系统维护 -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <h4 style="margin-bottom: 10px; color: #2c3e50;">系统维护</h4>
            <div style="margin-bottom: 10px;">
                <button onclick="clearCache()"
                        style="background: #9b59b6; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-right: 10px;">
                    清除缓存
                </button>
                <button onclick="exportLogs()"
                        style="background: #34495e; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    导出日志
                </button>
            </div>
            <div style="margin-top: 10px;">
                <button onclick="backupData()"
                        style="background: #16a085; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    备份数据
                </button>
            </div>
        </div>

        <!-- 安全设置 -->
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
            <h4 style="margin-bottom: 10px; color: #2c3e50;">安全设置</h4>
            <div style="margin-bottom: 10px;">
                <label style="display: flex; align-items: center; color: #7f8c8d;">
                    <input type="checkbox" checked style="margin-right: 8px;">
                    启用登录验证
                </label>
            </div>
            <div style="margin-bottom: 10px;">
                <label style="display: flex; align-items: center; color: #7f8c8d;">
                    <input type="checkbox" checked style="margin-right: 8px;">
                    记录操作日志
                </label>
            </div>
            <button onclick="changePassword()"
                    style="background: #e67e22; color: white; padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                修改密码
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 删除用户
function deleteUser(userId, username) {
    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销。`)) {
        window.location.href = `/admin/delete-user/${userId}`;
    }
}

// 刷新数据源
function refreshDataSource() {
    if (confirm('确定要刷新数据源吗？这可能需要一些时间。')) {
        // 显示加载状态
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = '刷新中...';
        btn.disabled = true;

        // 模拟刷新过程
        setTimeout(() => {
            btn.textContent = originalText;
            btn.disabled = false;
            alert('数据源刷新完成！');
        }, 2000);
    }
}

// 清除缓存
function clearCache() {
    if (confirm('确定要清除系统缓存吗？')) {
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = '清除中...';
        btn.disabled = true;

        setTimeout(() => {
            btn.textContent = originalText;
            btn.disabled = false;
            alert('缓存清除完成！');
        }, 1000);
    }
}

// 导出日志
function exportLogs() {
    const logContent = `系统日志导出
时间: ${new Date().toLocaleString()}
用户: {{ session.username }}
状态: 正常

最近操作:
- ${new Date().toLocaleString()}: 用户登录
- ${new Date(Date.now() - 3600000).toLocaleString()}: 数据查询
- ${new Date(Date.now() - 7200000).toLocaleString()}: 图表生成
`;

    const blob = new Blob([logContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system_logs_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// 备份数据
function backupData() {
    if (confirm('确定要备份系统数据吗？')) {
        const btn = event.target;
        const originalText = btn.textContent;
        btn.textContent = '备份中...';
        btn.disabled = true;

        setTimeout(() => {
            btn.textContent = originalText;
            btn.disabled = false;
            alert('数据备份完成！备份文件已保存到服务器。');
        }, 3000);
    }
}

// 修改密码
function changePassword() {
    const newPassword = prompt('请输入新密码:');
    if (newPassword && newPassword.length >= 6) {
        const confirmPassword = prompt('请再次输入新密码:');
        if (newPassword === confirmPassword) {
            alert('密码修改成功！请重新登录。');
            // 这里应该发送请求到服务器修改密码
        } else {
            alert('两次输入的密码不一致！');
        }
    } else if (newPassword !== null) {
        alert('密码长度至少为6位！');
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加一些初始化代码
    console.log('管理页面加载完成');
});
</script>
{% endblock %}
