<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}全球能耗数据可视化平台{% endblock %}</title>
    <!-- 引入CSS样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <!-- 引入ECharts (本地版本) -->
    <script src="{{ url_for('static', filename='js/lib/echarts.min.js') }}"></script>
    <!-- 引入本地世界地图数据 -->
    <script src="{{ url_for('static', filename='js/world.js') }}"></script>
    {% block extra_head %}{% endblock %}
    <style>
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 20px;
            background: #34495e;
            border-bottom: 1px solid #3d566e;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 18px;
            color: #ecf0f1;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            border-bottom: 1px solid #3d566e;
        }

        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #3498db;
            color: white;
        }

        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            margin-left: 250px;
            min-height: 100vh;
            background: #ecf0f1;
        }

        .top-navbar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            color: #2c3e50;
            margin: 0;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-name {
            color: #7f8c8d;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: #c0392b;
        }

        .content-area {
            padding: 30px;
        }

        .alert {
            padding: 12px 20px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-size: 14px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <!-- 侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>能耗数据平台</h2>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="{{ url_for('dashboard') }}" class="{% if request.endpoint == 'dashboard' %}active{% endif %}">
                    <i>📊</i> 仪表板
                </a>
            </li>
            <li>
                <a href="{{ url_for('energy_analysis') }}" class="{% if request.endpoint == 'energy_analysis' %}active{% endif %}">
                    <i>⚡</i> 能源分析
                </a>
            </li>
            <li>
                <a href="{{ url_for('country_comparison') }}" class="{% if request.endpoint == 'country_comparison' %}active{% endif %}">
                    <i>🌍</i> 国家对比
                </a>
            </li>
            <li>
                <a href="{{ url_for('data_management') }}" class="{% if request.endpoint == 'data_management' %}active{% endif %}">
                    <i>📋</i> 数据管理
                </a>
            </li>
            <li>
                <a href="{{ url_for('admin') }}" class="{% if request.endpoint == 'admin' %}active{% endif %}">
                    <i>⚙️</i> 系统管理
                </a>
            </li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <h1 class="page-title">{% block page_title %}仪表板{% endblock %}</h1>
            <div class="user-info">
                <span class="user-name">欢迎，{{ session.username }}</span>
                <a href="{{ url_for('logout') }}" class="logout-btn">登出</a>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            {% block content %}{% endblock %}
        </div>
    </div>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
