{% extends "base.html" %}

{% block title %}国家对比 - 全球能耗数据可视化平台{% endblock %}

{% block page_title %}国家对比分析{% endblock %}

{% block content %}
<!-- 国家选择器 -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
    <h3 style="margin-bottom: 15px; color: #2c3e50;">选择对比国家</h3>
    <div style="display: flex; gap: 20px; align-items: center;">
        <div>
            <label for="country-select-1" style="display: block; margin-bottom: 5px; color: #7f8c8d;">国家 1:</label>
            <select id="country-select-1" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; min-width: 150px;">
                <option value="">选择国家...</option>
            </select>
        </div>
        <div>
            <label for="country-select-2" style="display: block; margin-bottom: 5px; color: #7f8c8d;">国家 2:</label>
            <select id="country-select-2" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; min-width: 150px;">
                <option value="">选择国家...</option>
            </select>
        </div>
        <button id="compare-btn" onclick="updateComparison()" style="background: #3498db; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-top: 20px;">
            开始对比
        </button>
    </div>
</div>

<!-- 对比结果区域 -->
<div id="comparison-results" style="display: none;">
    <!-- 基本信息对比 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #2c3e50;" id="country1-title">国家 1</h3>
            <div id="country1-info">
                <div style="margin-bottom: 10px;">
                    <span style="color: #7f8c8d;">总能耗:</span>
                    <span id="country1-total" style="font-weight: bold; color: #2c3e50;">-</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <span style="color: #7f8c8d;">可再生能源占比:</span>
                    <span id="country1-renewable" style="font-weight: bold; color: #27ae60;">-</span>
                </div>
            </div>
        </div>

        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #2c3e50;" id="country2-title">国家 2</h3>
            <div id="country2-info">
                <div style="margin-bottom: 10px;">
                    <span style="color: #7f8c8d;">总能耗:</span>
                    <span id="country2-total" style="font-weight: bold; color: #2c3e50;">-</span>
                </div>
                <div style="margin-bottom: 10px;">
                    <span style="color: #7f8c8d;">可再生能源占比:</span>
                    <span id="country2-renewable" style="font-weight: bold; color: #27ae60;">-</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 能源结构对比图表 -->
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">能源结构对比</h3>
            <div id="energy-comparison-chart" class="chart" style="height: 400px;"></div>
        </div>

        <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="margin-bottom: 20px; color: #2c3e50;">能源类型分布</h3>
            <div id="energy-pie-comparison" class="chart" style="height: 400px;"></div>
        </div>
    </div>
</div>

<!-- 默认提示 -->
<div id="default-message" style="background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
    <h3 style="color: #7f8c8d; margin-bottom: 10px;">请选择两个国家进行对比</h3>
    <p style="color: #bdc3c7;">选择国家后点击"开始对比"按钮查看详细对比分析</p>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 数字格式化函数
function numberFormat(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toFixed(0);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadCountryList();
});

// 加载国家列表
function loadCountryList() {
    fetch('/api/data/countries')
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                console.error('加载国家列表错误:', result.error);
                return;
            }

            const countries = result.countries || [];
            const select1 = document.getElementById('country-select-1');
            const select2 = document.getElementById('country-select-2');

            countries.forEach(country => {
                const option1 = new Option(country, country);
                const option2 = new Option(country, country);
                select1.add(option1);
                select2.add(option2);
            });

            // 设置默认选择
            if (countries.length >= 2) {
                select1.value = countries[0];
                select2.value = countries[1];
            }
        })
        .catch(error => {
            console.error('获取国家列表失败:', error);
        });
}

// 更新对比
function updateComparison() {
    const country1 = document.getElementById('country-select-1').value;
    const country2 = document.getElementById('country-select-2').value;

    if (!country1 || !country2) {
        alert('请选择两个国家进行对比');
        return;
    }

    if (country1 === country2) {
        alert('请选择不同的国家进行对比');
        return;
    }

    // 隐藏默认消息，显示对比结果
    document.getElementById('default-message').style.display = 'none';
    document.getElementById('comparison-results').style.display = 'block';

    // 更新标题
    document.getElementById('country1-title').textContent = country1;
    document.getElementById('country2-title').textContent = country2;

    // 加载国家数据
    Promise.all([
        fetch(`/api/data/country/${country1}`).then(r => r.json()),
        fetch(`/api/data/country/${country2}`).then(r => r.json())
    ]).then(([data1, data2]) => {
        if (data1.error || data2.error) {
            console.error('加载国家数据错误:', data1.error || data2.error);
            return;
        }

        // 更新基本信息
        document.getElementById('country1-total').textContent = numberFormat(data1.total_electricity) + ' GWh';
        document.getElementById('country1-renewable').textContent = data1.renewable_percentage.toFixed(2) + '%';

        document.getElementById('country2-total').textContent = numberFormat(data2.total_electricity) + ' GWh';
        document.getElementById('country2-renewable').textContent = data2.renewable_percentage.toFixed(2) + '%';

        // 更新图表
        updateComparisonCharts(country1, country2, data1.energy_data, data2.energy_data);
    }).catch(error => {
        console.error('获取国家数据失败:', error);
    });
}

// 更新对比图表
function updateComparisonCharts(country1, country2, data1, data2) {
    // 能源结构对比柱状图
    const chartDom1 = document.getElementById('energy-comparison-chart');
    if (chartDom1) {
        const chart1 = echarts.init(chartDom1);

        // 合并所有能源类型
        const allEnergyTypes = new Set([...Object.keys(data1), ...Object.keys(data2)]);
        const categories = Array.from(allEnergyTypes);

        const series1Data = categories.map(type => data1[type] || 0);
        const series2Data = categories.map(type => data2[type] || 0);

        const option1 = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: [country1, country2]
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: categories,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'GWh'
            },
            series: [
                {
                    name: country1,
                    type: 'bar',
                    data: series1Data,
                    itemStyle: {
                        color: '#3498db'
                    }
                },
                {
                    name: country2,
                    type: 'bar',
                    data: series2Data,
                    itemStyle: {
                        color: '#e74c3c'
                    }
                }
            ]
        };

        chart1.setOption(option1);

        window.addEventListener('resize', function() {
            chart1.resize();
        });
    }

    // 能源类型分布饼图
    const chartDom2 = document.getElementById('energy-pie-comparison');
    if (chartDom2) {
        const chart2 = echarts.init(chartDom2);

        const pieData1 = Object.entries(data1).map(([key, value]) => ({
            name: key + ` (${country1})`,
            value: value
        }));

        const pieData2 = Object.entries(data2).map(([key, value]) => ({
            name: key + ` (${country2})`,
            value: value
        }));

        const option2 = {
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} GWh ({d}%)'
            },
            title: [
                {
                    text: country1,
                    left: '25%',
                    top: '5%',
                    textAlign: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                },
                {
                    text: country2,
                    left: '75%',
                    top: '5%',
                    textAlign: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'bold'
                    }
                }
            ],
            legend: {
                type: 'scroll',
                orient: 'horizontal',
                bottom: '2%',
                left: 'center',
                itemWidth: 10,
                itemHeight: 6,
                itemGap: 8,
                textStyle: {
                    fontSize: 8,
                    color: '#666'
                },
                pageButtonItemGap: 5,
                pageButtonGap: 10,
                pageIconColor: '#2f4554',
                pageIconInactiveColor: '#aaa',
                pageIconSize: 10,
                pageTextStyle: {
                    fontSize: 8,
                    color: '#666'
                },
                animation: true,
                animationDurationUpdate: 800
            },
            series: [
                {
                    name: country1,
                    type: 'pie',
                    radius: ['15%', '50%'],
                    center: ['25%', '45%'],
                    data: pieData1,
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: '{d}%',
                        fontSize: 9,
                        color: '#fff',
                        fontWeight: 'bold'
                    },
                    labelLine: {
                        show: false
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 12
                        }
                    }
                },
                {
                    name: country2,
                    type: 'pie',
                    radius: ['15%', '50%'],
                    center: ['75%', '45%'],
                    data: pieData2,
                    label: {
                        show: true,
                        position: 'inside',
                        formatter: '{d}%',
                        fontSize: 9,
                        color: '#fff',
                        fontWeight: 'bold'
                    },
                    labelLine: {
                        show: false
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 12
                        }
                    }
                }
            ]
        };

        chart2.setOption(option2);

        window.addEventListener('resize', function() {
            chart2.resize();
        });
    }
}
</script>
{% endblock %}
