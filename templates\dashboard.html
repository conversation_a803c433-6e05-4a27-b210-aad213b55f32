{% extends "base.html" %}

{% block title %}仪表板 - 全球能耗数据可视化平台{% endblock %}

{% block page_title %}仪表板概览{% endblock %}

{% block content %}
<!-- KPI卡片区 -->
<div class="kpi-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
    <div class="kpi-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">总能耗</h4>
        <div id="total-electricity" class="kpi-value" style="font-size: 24px; font-weight: bold; color: #2c3e50;">加载中...</div>
    </div>
    <div class="kpi-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">可再生能源占比</h4>
        <div id="renewable-rate" class="kpi-value" style="font-size: 24px; font-weight: bold; color: #27ae60;">加载中...</div>
    </div>
    <div class="kpi-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">最终消费量</h4>
        <div id="final-consumption" class="kpi-value" style="font-size: 24px; font-weight: bold; color: #e74c3c;">加载中...</div>
    </div>
</div>

<!-- 主要图表区 -->
<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; margin-bottom: 30px;">
    <!-- 世界地图 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">全球能耗分布</h3>
        <div id="world-map" class="chart" style="height: 400px;"></div>
    </div>

    <!-- 国家排名 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能耗排名</h3>
        <div id="country-ranking" class="chart" style="height: 400px;"></div>
    </div>
</div>

<!-- 下方图表区域 -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
    <!-- 能源类型分布 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能源类型分布</h3>
        <div id="energy-pie" class="chart" style="height: 300px;"></div>
    </div>

    <!-- 趋势线图 -->
    <div class="chart-container" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3 style="margin-bottom: 20px; color: #2c3e50;">能耗趋势</h3>
        <div id="trend-line" class="chart" style="height: 300px;"></div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// 数字格式化函数
function numberFormat(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toFixed(0);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有图表
    initWorldMap();
    initEnergyPie();
    initTrendLine();
    initCountryRanking();
    loadOverviewData();
});

// 加载概览数据
function loadOverviewData() {
    fetch('/api/data/overview')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('加载概览数据错误:', data.error);
                return;
            }

            // 更新KPI卡片
            document.getElementById('total-electricity').textContent =
                numberFormat(data.total_electricity) + ' GWh';

            document.getElementById('renewable-rate').textContent =
                data.renewable_percentage.toFixed(2) + '%';

            document.getElementById('final-consumption').textContent =
                numberFormat(data.total_electricity * 0.8) + ' GWh';
        })
        .catch(error => {
            console.error('获取概览数据失败:', error);
        });
}

// 初始化世界地图
function initWorldMap() {
    const chartDom = document.getElementById('world-map');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/chart/world-map')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载世界地图数据错误:', result.error);
                return;
            }

            const data = result.data || [];
            const maxValue = Math.max(...data.map(item => item.value));

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}<br/>{c} GWh'
                },
                visualMap: {
                    min: 0,
                    max: maxValue,
                    text: ['高', '低'],
                    calculable: true,
                    inRange: {
                        color: ['#0f3460', '#0088ff']
                    }
                },
                series: [
                    {
                        name: '能耗',
                        type: 'map',
                        map: 'world',
                        roam: true,
                        emphasis: {
                            label: {
                                show: true
                            }
                        },
                        data: data
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取世界地图数据失败:', error);
        });
}

// 初始化能源类型饼图
function initEnergyPie() {
    const chartDom = document.getElementById('energy-pie');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/data/overview')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载能源分布数据错误:', result.error);
                return;
            }

            const energyData = result.energy_distribution || {};
            const pieData = Object.entries(energyData).map(([key, value]) => ({
                name: key,
                value: value
            }));

            const option = {
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} GWh ({d}%)'
                },
                legend: {
                    type: 'scroll',
                    orient: 'vertical',
                    right: '5%',
                    top: 'center',
                    itemWidth: 12,
                    itemHeight: 8,
                    itemGap: 6,
                    textStyle: {
                        fontSize: 10,
                        color: '#666'
                    },
                    pageButtonItemGap: 5,
                    pageIconSize: 8
                },
                series: [
                    {
                        name: '能源类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        data: pieData
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取能源分布数据失败:', error);
        });
}

// 初始化趋势线图
function initTrendLine() {
    const chartDom = document.getElementById('trend-line');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/chart/trend-line')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载趋势线图数据错误:', result.error);
                return;
            }

            const categories = result.categories || [];
            const seriesData = result.series || [];

            const series = seriesData.map(item => ({
                name: item.name,
                type: 'line',
                smooth: true,
                data: item.data
            }));

            const option = {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: seriesData.map(item => item.name)
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: categories
                },
                yAxis: {
                    type: 'value'
                },
                series: series
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取趋势线图数据失败:', error);
        });
}

// 初始化国家排名
function initCountryRanking() {
    const chartDom = document.getElementById('country-ranking');
    if (!chartDom) return;

    const chart = echarts.init(chartDom);
    chart.showLoading();

    fetch('/api/chart/country-ranking')
        .then(response => response.json())
        .then(result => {
            chart.hideLoading();

            if (result.error) {
                console.error('加载国家排名数据错误:', result.error);
                return;
            }

            const rankingData = result.ranking || [];
            const countries = rankingData.map(item => item.country).reverse();
            const values = rankingData.map(item => item.value).reverse();

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value'
                },
                yAxis: {
                    type: 'category',
                    data: countries
                },
                series: [
                    {
                        name: '能耗',
                        type: 'bar',
                        data: values,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                { offset: 0, color: '#0f3460' },
                                { offset: 1, color: '#0088ff' }
                            ])
                        }
                    }
                ]
            };

            chart.setOption(option);

            window.addEventListener('resize', function() {
                chart.resize();
            });
        })
        .catch(error => {
            chart.hideLoading();
            console.error('获取国家排名数据失败:', error);
        });
}
</script>
{% endblock %}
