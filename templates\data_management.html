{% extends "base.html" %}

{% block title %}数据管理 - 全球能耗数据可视化平台{% endblock %}

{% block page_title %}数据管理{% endblock %}

{% block content %}
<!-- 数据概览 -->
<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">总记录数</h4>
        <div id="total-records" style="font-size: 24px; font-weight: bold; color: #2c3e50;">-</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">国家数量</h4>
        <div id="total-countries" style="font-size: 24px; font-weight: bold; color: #3498db;">-</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">能源类型</h4>
        <div id="total-energy-types" style="font-size: 24px; font-weight: bold; color: #27ae60;">-</div>
    </div>
    <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h4 style="color: #7f8c8d; margin-bottom: 10px;">数据更新时间</h4>
        <div style="font-size: 14px; color: #2c3e50;">{{ now.strftime('%Y-%m-%d %H:%M') }}</div>
    </div>
</div>

<!-- 数据筛选和搜索 -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px;">
    <h3 style="margin-bottom: 15px; color: #2c3e50;">数据筛选</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <label style="display: block; margin-bottom: 5px; color: #7f8c8d;">国家筛选:</label>
            <select id="country-filter" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">所有国家</option>
            </select>
        </div>
        <div>
            <label style="display: block; margin-bottom: 5px; color: #7f8c8d;">能源类型筛选:</label>
            <select id="energy-filter" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                <option value="">所有能源类型</option>
            </select>
        </div>
        <div>
            <label style="display: block; margin-bottom: 5px; color: #7f8c8d;">搜索:</label>
            <input type="text" id="search-input" placeholder="搜索国家或能源类型..." style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
        </div>
        <div style="display: flex; align-items: end;">
            <button onclick="applyFilters()" style="background: #3498db; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                应用筛选
            </button>
        </div>
    </div>
</div>

<!-- 数据表格 -->
<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h3 style="color: #2c3e50; margin: 0;">数据详情</h3>
        <div>
            <button onclick="exportData()" style="background: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                导出数据
            </button>
            <button onclick="refreshData()" style="background: #f39c12; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                刷新数据
            </button>
        </div>
    </div>
    
    <div style="overflow-x: auto; max-height: 600px; overflow-y: auto;">
        <table id="data-table" style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f8f9fa; position: sticky; top: 0;">
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6; cursor: pointer;" onclick="sortTable(0)">
                        国家 ↕
                    </th>
                    <th style="padding: 12px; text-align: left; border-bottom: 2px solid #dee2e6; cursor: pointer;" onclick="sortTable(1)">
                        能源类型 ↕
                    </th>
                    <th style="padding: 12px; text-align: right; border-bottom: 2px solid #dee2e6; cursor: pointer;" onclick="sortTable(2)">
                        数值 (GWh) ↕
                    </th>
                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #dee2e6;">
                        操作
                    </th>
                </tr>
            </thead>
            <tbody id="data-tbody">
                <tr>
                    <td colspan="4" style="text-align: center; padding: 40px; color: #7f8c8d;">
                        加载中...
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <div id="pagination" style="margin-top: 20px; text-align: center;">
        <!-- 分页控件将通过JavaScript生成 -->
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentData = [];
let filteredData = [];
let currentPage = 1;
const itemsPerPage = 50;

// 数字格式化函数
function numberFormat(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toFixed(0);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    loadFilters();
});

// 加载数据
function loadData() {
    // 模拟加载CSV数据
    fetch('/api/data/overview')
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                console.error('加载数据错误:', result.error);
                return;
            }
            
            // 更新统计信息
            document.getElementById('total-countries').textContent = result.country_count || 0;
            document.getElementById('total-energy-types').textContent = result.energy_types || 0;
            
            // 模拟详细数据（实际应用中应该有专门的API）
            generateSampleData(result);
        })
        .catch(error => {
            console.error('获取数据失败:', error);
        });
}

// 生成示例数据
function generateSampleData(overview) {
    const countries = ['China', 'United States', 'India', 'Russia', 'Japan', 'Germany', 'Canada', 'Brazil', 'South Korea', 'Iran'];
    const energyTypes = Object.keys(overview.energy_distribution || {});
    
    currentData = [];
    let totalRecords = 0;
    
    countries.forEach(country => {
        energyTypes.forEach(energyType => {
            const value = Math.random() * 10000 + 1000;
            currentData.push({
                country: country,
                energyType: energyType,
                value: value
            });
            totalRecords++;
        });
    });
    
    document.getElementById('total-records').textContent = totalRecords;
    filteredData = [...currentData];
    displayData();
}

// 加载筛选器选项
function loadFilters() {
    fetch('/api/data/countries')
        .then(response => response.json())
        .then(result => {
            if (result.error) return;
            
            const countryFilter = document.getElementById('country-filter');
            result.countries.forEach(country => {
                const option = new Option(country, country);
                countryFilter.add(option);
            });
        });
    
    fetch('/api/data/overview')
        .then(response => response.json())
        .then(result => {
            if (result.error) return;
            
            const energyFilter = document.getElementById('energy-filter');
            Object.keys(result.energy_distribution || {}).forEach(energyType => {
                const option = new Option(energyType, energyType);
                energyFilter.add(option);
            });
        });
}

// 应用筛选
function applyFilters() {
    const countryFilter = document.getElementById('country-filter').value;
    const energyFilter = document.getElementById('energy-filter').value;
    const searchInput = document.getElementById('search-input').value.toLowerCase();
    
    filteredData = currentData.filter(item => {
        const matchCountry = !countryFilter || item.country === countryFilter;
        const matchEnergy = !energyFilter || item.energyType === energyFilter;
        const matchSearch = !searchInput || 
            item.country.toLowerCase().includes(searchInput) || 
            item.energyType.toLowerCase().includes(searchInput);
        
        return matchCountry && matchEnergy && matchSearch;
    });
    
    currentPage = 1;
    displayData();
}

// 显示数据
function displayData() {
    const tbody = document.getElementById('data-tbody');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #7f8c8d;">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = pageData.map((item, index) => `
        <tr style="border-bottom: 1px solid #dee2e6; ${(startIndex + index) % 2 === 0 ? 'background: #f8f9fa;' : ''}">
            <td style="padding: 12px; color: #2c3e50;">${item.country}</td>
            <td style="padding: 12px; color: #2c3e50;">${item.energyType}</td>
            <td style="padding: 12px; text-align: right; color: #2c3e50;">${numberFormat(item.value)}</td>
            <td style="padding: 12px; text-align: center;">
                <button onclick="viewDetails('${item.country}', '${item.energyType}')" 
                        style="background: #3498db; color: white; padding: 4px 8px; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">
                    详情
                </button>
            </td>
        </tr>
    `).join('');
    
    updatePagination();
}

// 更新分页
function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (currentPage > 1) {
        paginationHTML += `<button onclick="changePage(${currentPage - 1})" style="margin: 0 5px; padding: 8px 12px; border: 1px solid #ddd; background: white; cursor: pointer;">上一页</button>`;
    }
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const isActive = i === currentPage;
        paginationHTML += `<button onclick="changePage(${i})" style="margin: 0 2px; padding: 8px 12px; border: 1px solid #ddd; background: ${isActive ? '#3498db' : 'white'}; color: ${isActive ? 'white' : 'black'}; cursor: pointer;">${i}</button>`;
    }
    
    // 下一页
    if (currentPage < totalPages) {
        paginationHTML += `<button onclick="changePage(${currentPage + 1})" style="margin: 0 5px; padding: 8px 12px; border: 1px solid #ddd; background: white; cursor: pointer;">下一页</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    currentPage = page;
    displayData();
}

// 表格排序
function sortTable(columnIndex) {
    const isAscending = !filteredData._sortAscending || filteredData._lastSortColumn !== columnIndex;
    
    filteredData.sort((a, b) => {
        let aVal, bVal;
        
        switch (columnIndex) {
            case 0: // 国家
                aVal = a.country;
                bVal = b.country;
                break;
            case 1: // 能源类型
                aVal = a.energyType;
                bVal = b.energyType;
                break;
            case 2: // 数值
                aVal = a.value;
                bVal = b.value;
                break;
        }
        
        if (columnIndex === 2) { // 数值列
            return isAscending ? aVal - bVal : bVal - aVal;
        } else { // 文本列
            return isAscending ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
        }
    });
    
    filteredData._sortAscending = isAscending;
    filteredData._lastSortColumn = columnIndex;
    
    currentPage = 1;
    displayData();
}

// 查看详情
function viewDetails(country, energyType) {
    alert(`国家: ${country}\n能源类型: ${energyType}\n\n点击确定查看更多详情...`);
}

// 导出数据
function exportData() {
    const csvContent = "data:text/csv;charset=utf-8," 
        + "国家,能源类型,数值(GWh)\n"
        + filteredData.map(item => `${item.country},${item.energyType},${item.value}`).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "energy_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 刷新数据
function refreshData() {
    document.getElementById('data-tbody').innerHTML = '<tr><td colspan="4" style="text-align: center; padding: 40px; color: #7f8c8d;">刷新中...</td></tr>';
    setTimeout(() => {
        loadData();
    }, 1000);
}
</script>
{% endblock %}
