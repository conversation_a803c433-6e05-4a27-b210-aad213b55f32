#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API接口的脚本
"""

import requests
import json

def test_api():
    base_url = "http://localhost:5000"
    
    # 测试概览数据API
    print("测试概览数据API...")
    try:
        response = requests.get(f"{base_url}/api/data/overview")
        if response.status_code == 200:
            data = response.json()
            print("✅ 概览数据API正常")
            print(f"能源分布: {list(data.get('energy_distribution', {}).keys())}")
        else:
            print(f"❌ 概览数据API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 概览数据API异常: {e}")
    
    # 测试能源排序API
    print("\n测试能源排序API...")
    try:
        response = requests.get(f"{base_url}/api/data/energy-ranking")
        if response.status_code == 200:
            data = response.json()
            print("✅ 能源排序API正常")
            ranking = data.get('ranking', [])
            if ranking:
                print(f"前3名能源类型: {[item['energy_type'] for item in ranking[:3]]}")
        else:
            print(f"❌ 能源排序API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 能源排序API异常: {e}")
    
    # 测试国家列表API
    print("\n测试国家列表API...")
    try:
        response = requests.get(f"{base_url}/api/data/countries")
        if response.status_code == 200:
            data = response.json()
            print("✅ 国家列表API正常")
            countries = data.get('countries', [])
            print(f"国家数量: {len(countries)}")
            if countries:
                print(f"前5个国家: {countries[:5]}")
        else:
            print(f"❌ 国家列表API错误: {response.status_code}")
    except Exception as e:
        print(f"❌ 国家列表API异常: {e}")

if __name__ == "__main__":
    print("开始测试API接口...")
    test_api()
    print("\n测试完成！")
